import './styles/index.css'; // Entry point for styles
// Import the new EditorCore class and its dependencies
import { EditorCore } from './editor/core';
import { HistoryManager } from './history/history-manager';
import { ClipboardManager } from './clipboard/clipboard-manager';
import { Renderer } from './editor/renderer';
import { FormattingManager } from './editor/formatting-manager'; // Import FormattingManager
import { SelectionManager } from './editor/selection-manager';
import { PluginManager } from './plugins/plugin-manager';
// ThemeManager will be imported dynamically
import { setupThemeObserver } from './themes/theme-helper'; // Import theme helper
import './toolbar/toolbar-button'; // Import to register the custom element

// Import toolbar layout manager
import { ToolbarLayoutManager } from './toolbar/layout';
import { allPlugins } from './plugins';

// Initialize the editor when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  // Initialize editor core
  const editorElement = document.querySelector('#editor');

  // Add checks for null and HTMLElement type
  if (!editorElement) {
    console.error('Editor element #editor not found.');
    return;
  }
  if (!(editorElement instanceof HTMLElement)) {
    console.error('Element #editor is not an HTMLElement.');
    return;
  }

  // Initialize theme manager first for dependency injection
  // For now, we'll use a simple approach that maintains backward compatibility
  let themeManager: any = null;
  let elementFactory: any = null;
  try {
    // Try to create enhanced theme manager with minimal dependencies
    const { createConfiguredThemeContainer } = await import('./themes/dependency-injection/theme-service-setup');
    const { ThemeManager } = await import('./themes/theme-manager');
    const { ThemeElementFactory } = await import('./themes/element-factory');
    const { SERVICE_TOKENS } = await import('./themes/interfaces/core-interfaces');

    const container = createConfiguredThemeContainer();
    const configManager = container.resolve(SERVICE_TOKENS.CONFIG_MANAGER) as any;
    const cssInjector = container.resolve(SERVICE_TOKENS.CSS_VARIABLE_INJECTOR) as any;
    const storage = container.resolve(SERVICE_TOKENS.THEME_STORAGE) as any;
    const validator = container.resolve(SERVICE_TOKENS.VALIDATOR) as any;

    themeManager = new ThemeManager(
      configManager,
      cssInjector,
      storage,
      validator,
      document,
      window
      // Optional dependencies omitted for simplicity
    );

    await themeManager.init();

    // Create element factory with theme manager integration
    const fallbackLogger = {
      logOperationStart: () => {},
      logOperationComplete: () => {},
      logError: () => {},
      logUserAction: () => {},
      createChild: () => fallbackLogger
    };

    const fallbackPerformanceMonitor = {
      measureOperation: async <T>(_op: unknown, fn: () => Promise<T>): Promise<T> => fn(),
      measureOperationSync: <T>(_op: unknown, fn: () => T): T => fn(),
      setPerformanceThreshold: () => {},
      getPerformanceStats: () => ({
        operationCounts: {},
        averageDurations: {},
        benchmarkViolations: {},
        memoryTrend: 'unknown' as const
      })
    };

    elementFactory = new ThemeElementFactory(
      themeManager.getCurrentTheme(),
      fallbackLogger,
      fallbackPerformanceMonitor
    );

    // Set up theme change listener to update element factory
    themeManager.watch((theme: unknown) => {
      elementFactory.setCurrentTheme(theme);
    });

    // Expose theme system globally for plugins
    (window as Window & { featherThemeManager?: unknown; featherElementFactory?: unknown }).featherThemeManager = themeManager;
    (window as Window & { featherThemeManager?: unknown; featherElementFactory?: unknown }).featherElementFactory = elementFactory;

    console.log('Enhanced theme manager and element factory initialized successfully');
  } catch (error) {
    console.warn('Enhanced theme manager failed to initialize, using minimal theme support:', error);
    // For now, just use null - components will work without theme manager
    themeManager = null;
    elementFactory = null;
  }

  // 1. Instantiate Dependencies with optional theme manager
  const renderer = new Renderer(themeManager);
  const selectionManager = new SelectionManager(themeManager);
  const historyManager = new HistoryManager({}, themeManager); // Pass theme manager as second parameter
  const clipboardManager = new ClipboardManager(themeManager); // Needs initialization
  const formattingManager = new FormattingManager(themeManager); // Instantiate FormattingManager

  // 2. Instantiate EditorCore with dependencies
  const editor = new EditorCore(
    editorElement,      // 1. element
    historyManager,     // 2. historyManager
    clipboardManager,   // 3. clipboardManager
    formattingManager,  // 4. formattingManager
    renderer,           // 5. renderer
    selectionManager,   // 6. selectionManager
    themeManager        // 7. themeManager (optional)
  );

  // 3. Initialize ClipboardManager (and others if needed)
  // Initialize components that require the element after construction
  renderer.initialize(editorElement);
  selectionManager.initialize(editorElement);
  clipboardManager.initialize(editorElement, selectionManager, renderer);
  formattingManager.initialize(selectionManager, renderer); // Initialize FormattingManager (Corrected order)

  // Initialize plugin manager
  const pluginManager = new PluginManager();

  // Register all plugins

  allPlugins.plugins.forEach(plugin => {
    pluginManager.register(plugin);
  });

  // Initialize toolbar layout
  const toolbarElement = document.getElementById('toolbar');
  if (toolbarElement) {
    const toolbarLayoutManager = new ToolbarLayoutManager();
    toolbarLayoutManager.initialize(toolbarElement, pluginManager);
  } else {
    console.warn('Toolbar element not found. Toolbar layout will not be initialized.');
  }

  // Initialize plugins
  pluginManager.initAll(editor); // Pass the EditorCore instance

  // Setup theme observer for plugin UI elements
  setupThemeObserver();

  // Dispatch editor-ready event
  document.dispatchEvent(new CustomEvent('editor-ready', {
    detail: { editor } // Pass the EditorCore instance
  }));
});