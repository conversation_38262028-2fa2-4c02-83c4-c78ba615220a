/* --------------------------------------------------------------------------
 *  Plugin Manager
 *  ──────────────
 *  • Strong typing – only accepts objects that conform to Plugin
 *  • Prevents double-registration and double-initialisation
 *  • Allows init/destroy for ALL or ONE plugin
 *  • Stores plugin references in a Map for O(1) access
 * ------------------------------------------------------------------------ */

import type { Editor, Plugin } from '@/types';

export class PluginManager {
  /** All registered plugins (`id` → definition) */
  private readonly registry = new Map<string, Plugin>();

  /** Which plugins have already been initialised for the current editor */
  private readonly initialised = new Set<string>();

  /** The editor this manager is bound to (set on `initAll`) */
  private editorInstance: Editor | null = null;

  /* -------------------------------------------------------------------- */
  /*  Register                                                             */
  /* -------------------------------------------------------------------- */

  /**
   * Register a single plugin definition
   * @throws If plugin id duplicates an existing registration
   */
  register(plugin: Plugin): void {
    if (this.registry.has(plugin.id)) {
      throw new Error(`Plugin "${plugin.id}" already registered.`);
    }
    console.log(`[PluginManager] Registering plugin: ${plugin.id}, toolbarItems:`, plugin.toolbarItems?.length || 0);
    this.registry.set(plugin.id, plugin);
    /* Do NOT init here – caller chooses when by calling init / initAll */
  }

  /**
   * Convenience: register many at once
   */
  registerMany(plugins: Plugin[]): void {
    plugins.forEach((p) => this.register(p));
  }

  /* -------------------------------------------------------------------- */
  /*  Initialise                                                           */
  /* -------------------------------------------------------------------- */

  /**
   * Initialise **all** registered plugins (once) for an editor instance.
   */
  initAll(editor: Editor): void {
    this.editorInstance = editor;
    this.registry.forEach((plugin) => this.safeInit(plugin, editor));
  }

  /**
   * Initialise a **single** plugin by id.
   */
  init(id: string, editor?: Editor): void {
    const plugin = this.registry.get(id);
    if (!plugin) {
      console.warn(`Plugin "${id}" is not registered.`);
      return;
    }
    /* Prefer the editor passed to init(); else fall back on the stored one */
    const ed = editor ?? this.editorInstance;
    if (!ed) {
      console.error(
        `No editor instance supplied – call init('${id}', editor) or initAll(editor) first.`
      );
      return;
    }
    this.safeInit(plugin, ed);
  }

  private safeInit(plugin: Plugin, editor: Editor): void {
    if (this.initialised.has(plugin.id)) return;
    try {
      plugin.init(editor);
      this.initialised.add(plugin.id);
      console.info(`Plugin "${plugin.id}" initialised.`);
    } catch (err) {
      console.error(`Plugin "${plugin.id}" failed to initialise:`, err);
    }
  }

  /* -------------------------------------------------------------------- */
  /*  Destruction                                                          */
  /* -------------------------------------------------------------------- */

  /**
   * Destroy **all** initialised plugins.
   */
  destroyAll(): void {
    this.initialised.forEach((id) => this.destroy(id));
    this.initialised.clear();
  }

  /**
   * Destroy a **single** plugin by id.
   */
  destroy(id: string): void {
    const plugin = this.registry.get(id);
    if (!plugin || !this.initialised.has(id)) return;

    try {
      if (typeof plugin.destroy === 'function') {
        plugin.destroy();
        console.info(`Plugin "${id}" destroyed.`);
      }
    } catch (err) {
      console.error(`Plugin "${id}" failed to destroy:`, err);
    } finally {
      this.initialised.delete(id);
    }
  }

  /* -------------------------------------------------------------------- */
  /*  Accessor                                                             */
  /* -------------------------------------------------------------------- */

  /**
   * Get a registered plugin definition (whether initialised or not).
   */
  get(id: string): Plugin | undefined {
    return this.registry.get(id);
  }
}