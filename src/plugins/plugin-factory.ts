import type { Editor, Plugin, ToolbarItem } from '../types';

/**
 * Factory function to create a plugin that conforms to the Plugin interface
 * @param id The plugin ID
 * @param initFn The plugin initialization function
 * @param destroyFn Optional destruction function
 * @param toolbarItems Optional toolbar items for the plugin
 * @returns A Plugin object
 */
export function createPlugin<T>(
  id: string,
  initFn: (editor: Editor) => T,
  destroyFn?: (instance: T) => void,
  toolbarItems?: ToolbarItem[]
): Plugin {
  let pluginInstance: T | null = null;

  return {
    id,
    toolbarItems,
    init: (editor: Editor) => {
      pluginInstance = initFn(editor);
    },
    destroy: () => {
      if (destroyFn && pluginInstance) {
        destroyFn(pluginInstance);
        pluginInstance = null;
      }
    }
  };
}
